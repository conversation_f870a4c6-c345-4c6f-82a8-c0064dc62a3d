<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>initializeLevel 方法修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a2e;
            color: #fff;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #16213e;
            border-radius: 10px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            background: #0f3460;
            border-radius: 5px;
        }
        .test-button {
            background: #e94560;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #d63447;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #27ae60;
        }
        .error {
            background: #e74c3c;
        }
        .warning {
            background: #f39c12;
        }
        #console-output {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>initializeLevel 方法修复测试</h1>
        <p>测试 GameController.initializeLevel 方法是否正常工作</p>

        <div class="test-section">
            <h3>测试步骤</h3>
            <button class="test-button" onclick="testInitializeLevel()">测试 initializeLevel 方法</button>
            <button class="test-button" onclick="testStartLevel()">测试 startLevel 方法</button>
            <button class="test-button" onclick="clearConsole()">清空控制台</button>
        </div>

        <div class="test-section">
            <h3>测试结果</h3>
            <div id="test-results"></div>
        </div>

        <div class="test-section">
            <h3>控制台输出</h3>
            <div id="console-output"></div>
        </div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/utils/math-utils.js"></script>
    <script src="js/core/physics-engine.js"></script>
    <script src="js/core/quantum-engine.js"></script>
    <script src="js/core/audio-engine.js"></script>
    <script src="js/game/level.js"></script>
    <script src="js/game/level-manager.js"></script>
    <script src="js/game/game-controller.js"></script>

    <script>
        // 重写 console.log 以显示在页面上
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        
        const consoleOutput = document.getElementById('console-output');
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : '📋';
            consoleOutput.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };

        function addTestResult(message, type = 'success') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        function clearConsole() {
            consoleOutput.textContent = '';
            document.getElementById('test-results').innerHTML = '';
        }

        async function testInitializeLevel() {
            console.log('🧪 开始测试 initializeLevel 方法...');
            
            try {
                // 检查 GameController 是否存在
                if (!window.gameController) {
                    throw new Error('GameController 不存在');
                }
                
                // 检查 initializeLevel 方法是否存在
                if (typeof gameController.initializeLevel !== 'function') {
                    throw new Error('initializeLevel 方法不存在');
                }
                
                addTestResult('✅ GameController 和 initializeLevel 方法存在', 'success');
                
                // 初始化游戏控制器
                if (!gameController.isInitialized) {
                    console.log('🎮 初始化游戏控制器...');
                    await gameController.init();
                }
                
                // 创建测试关卡配置
                const testLevelConfig = {
                    id: 'test-level',
                    name: '测试关卡',
                    description: '用于测试 initializeLevel 方法的关卡',
                    particles: [
                        { x: 0.3, y: 0.3, frequency: 440, energy: 50 },
                        { x: 0.7, y: 0.3, frequency: 880, energy: 50 }
                    ],
                    connections: [
                        { from: 0, to: 1, strength: 0.5 }
                    ],
                    objectives: [
                        { type: 'activate_particles', target: 2, description: '激活所有粒子' }
                    ],
                    timeLimit: 60,
                    targetScore: 1000
                };
                
                // 测试 initializeLevel 方法
                console.log('🧪 调用 initializeLevel 方法...');
                await gameController.initializeLevel(testLevelConfig);
                
                addTestResult('✅ initializeLevel 方法调用成功', 'success');
                
                // 检查关卡是否正确设置
                if (gameController.currentLevel) {
                    addTestResult('✅ 当前关卡已设置', 'success');
                    console.log('📋 当前关卡:', gameController.currentLevel);
                } else {
                    addTestResult('⚠️ 当前关卡未设置', 'warning');
                }
                
            } catch (error) {
                console.error('❌ 测试失败:', error);
                addTestResult(`❌ 测试失败: ${error.message}`, 'error');
            }
        }

        async function testStartLevel() {
            console.log('🧪 开始测试 startLevel 方法...');
            
            try {
                // 检查 GameController 是否存在
                if (!window.gameController) {
                    throw new Error('GameController 不存在');
                }
                
                // 初始化游戏控制器
                if (!gameController.isInitialized) {
                    console.log('🎮 初始化游戏控制器...');
                    await gameController.init();
                }
                
                // 初始化关卡管理器
                if (window.levelManager && !levelManager.isInitialized) {
                    console.log('🎯 初始化关卡管理器...');
                    levelManager.init();
                }
                
                // 测试 startLevel 方法
                console.log('🧪 调用 startLevel 方法...');
                await gameController.startLevel('tutorial', 'easy');
                
                addTestResult('✅ startLevel 方法调用成功', 'success');
                
            } catch (error) {
                console.error('❌ 测试失败:', error);
                addTestResult(`❌ 测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后自动运行基础测试
        window.addEventListener('load', () => {
            console.log('🎮 页面加载完成，开始基础检查...');
            
            // 检查必要的类和对象是否存在
            const requiredObjects = [
                'GameController',
                'LevelManager', 
                'Level',
                'PhysicsEngine',
                'QuantumEngine',
                'AudioEngine'
            ];
            
            requiredObjects.forEach(objName => {
                if (window[objName]) {
                    console.log(`✅ ${objName} 类存在`);
                } else {
                    console.warn(`⚠️ ${objName} 类不存在`);
                }
            });
            
            // 检查全局实例
            const requiredInstances = [
                'gameController',
                'levelManager',
                'physicsEngine',
                'quantumEngine',
                'audioEngine'
            ];
            
            requiredInstances.forEach(instanceName => {
                if (window[instanceName]) {
                    console.log(`✅ ${instanceName} 实例存在`);
                } else {
                    console.warn(`⚠️ ${instanceName} 实例不存在`);
                }
            });
            
            console.log('🎮 基础检查完成');
        });
    </script>
</body>
</html>
